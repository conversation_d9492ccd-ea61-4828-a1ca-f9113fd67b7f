#!/usr/bin/env python3
"""
Test script to verify CSV file move functionality
"""

import os
import sys
import shutil
import tempfile

# Add the current directory to Python path to import our module
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_csv_move_functionality():
    """Test the CSV file move functionality"""
    print("Testing CSV file move functionality...")
    
    # Create a temporary test environment
    test_downloads_dir = "/tmp/test_downloads"
    test_processed_dir = os.path.join(test_downloads_dir, "processed")
    
    # Clean up any existing test directory
    if os.path.exists(test_downloads_dir):
        shutil.rmtree(test_downloads_dir)
    
    # Create test directories
    os.makedirs(test_downloads_dir, exist_ok=True)
    print(f"Created test downloads directory: {test_downloads_dir}")
    
    # Create test CSV files
    test_files = [
        "test-file-1.csv",
        "Seerfar-Product-Test-123.csv",
        "uuid-based-file-456.csv"
    ]
    
    for filename in test_files:
        file_path = os.path.join(test_downloads_dir, filename)
        with open(file_path, 'w') as f:
            f.write("NO.,Title,Price\n1,Test Product,100\n2,Another Product,200\n")
        print(f"Created test file: {filename}")
    
    # Test the move function
    try:
        # Import the function from our module
        from e2b_browser_use import move_csv_files_to_processed
        
        print(f"\nTesting move_csv_files_to_processed function...")
        moved_files = move_csv_files_to_processed(test_downloads_dir)
        
        print(f"Function returned {len(moved_files)} moved files:")
        for file_path in moved_files:
            print(f"  - {os.path.basename(file_path)}")
        
        # Verify results
        print(f"\nVerifying results...")
        
        # Check that downloads directory is empty of CSV files
        remaining_csv = [f for f in os.listdir(test_downloads_dir) 
                        if f.endswith('.csv') and os.path.isfile(os.path.join(test_downloads_dir, f))]
        
        if remaining_csv:
            print(f"❌ ERROR: {len(remaining_csv)} CSV files still in downloads directory:")
            for f in remaining_csv:
                print(f"  - {f}")
        else:
            print(f"✅ SUCCESS: Downloads directory is clean of CSV files")
        
        # Check that processed directory has the files
        if os.path.exists(test_processed_dir):
            processed_files = [f for f in os.listdir(test_processed_dir) if f.endswith('.csv')]
            print(f"✅ SUCCESS: Found {len(processed_files)} CSV files in processed directory:")
            for f in processed_files:
                print(f"  - {f}")
        else:
            print(f"❌ ERROR: Processed directory does not exist")
        
        # Test with existing files (conflict resolution)
        print(f"\nTesting conflict resolution...")
        
        # Create another file with the same name
        conflict_file = os.path.join(test_downloads_dir, "test-file-1.csv")
        with open(conflict_file, 'w') as f:
            f.write("NO.,Title,Price\n1,Conflict Test,999\n")
        print(f"Created conflict file: test-file-1.csv")
        
        # Run move function again
        moved_files_2 = move_csv_files_to_processed(test_downloads_dir)
        print(f"Second run moved {len(moved_files_2)} files")
        
        # Check processed directory again
        processed_files_final = [f for f in os.listdir(test_processed_dir) if f.endswith('.csv')]
        print(f"✅ Final processed directory has {len(processed_files_final)} CSV files:")
        for f in processed_files_final:
            print(f"  - {f}")
        
        print(f"\n🎉 Test completed successfully!")
        
    except Exception as e:
        print(f"❌ ERROR during testing: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up test directory
        if os.path.exists(test_downloads_dir):
            shutil.rmtree(test_downloads_dir)
            print(f"\nCleaned up test directory: {test_downloads_dir}")

if __name__ == "__main__":
    test_csv_move_functionality()
