#!/usr/bin/env python3
"""
Standalone test for CSV move functionality
"""

import os
import shutil
import glob
import time

def move_csv_files_to_processed_test(downloads_dir):
    """Test version of the move function"""
    print(f"Moving CSV files to processed directory: {downloads_dir}")
    
    processed_dir = os.path.join(downloads_dir, "processed")
    os.makedirs(processed_dir, exist_ok=True)
    
    # Find all CSV files in downloads directory (excluding processed subdirectory)
    csv_files = []
    for file in glob.glob(os.path.join(downloads_dir, "*.csv")):
        if 'processed' not in file and os.path.isfile(file):
            csv_files.append(file)
    
    print(f"Found {len(csv_files)} CSV files to move to processed directory")
    
    moved_files = []
    for file_path in csv_files:
        try:
            filename = os.path.basename(file_path)
            processed_file_path = os.path.join(processed_dir, filename)
            
            # Check if file already exists in processed directory
            if os.path.exists(processed_file_path):
                # Add timestamp to avoid conflicts
                timestamp = int(time.time())
                name, ext = os.path.splitext(filename)
                new_filename = f"{name}_{timestamp}{ext}"
                processed_file_path = os.path.join(processed_dir, new_filename)
                print(f"File exists, using new name: {new_filename}")
            
            # Move the file
            shutil.move(file_path, processed_file_path)
            moved_files.append(processed_file_path)
            print(f"Moved {filename} to processed/{os.path.basename(processed_file_path)}")
            
        except Exception as e:
            print(f"Error moving {os.path.basename(file_path)}: {e}")
    
    print(f"Successfully moved {len(moved_files)} CSV files to processed directory")
    return moved_files

def test_functionality():
    """Test the CSV file move functionality"""
    print("Testing CSV file move functionality...")
    
    # Create a temporary test environment
    test_downloads_dir = "/tmp/test_downloads_standalone"
    test_processed_dir = os.path.join(test_downloads_dir, "processed")
    
    # Clean up any existing test directory
    if os.path.exists(test_downloads_dir):
        shutil.rmtree(test_downloads_dir)
    
    # Create test directories
    os.makedirs(test_downloads_dir, exist_ok=True)
    print(f"Created test downloads directory: {test_downloads_dir}")
    
    # Create test CSV files
    test_files = [
        "test-file-1.csv",
        "Seerfar-Product-Test-123.csv",
        "uuid-based-file-456.csv"
    ]
    
    for filename in test_files:
        file_path = os.path.join(test_downloads_dir, filename)
        with open(file_path, 'w') as f:
            f.write("NO.,Title,Price\n1,Test Product,100\n2,Another Product,200\n")
        print(f"Created test file: {filename}")
    
    # Test the move function
    try:
        print(f"\nTesting move function...")
        moved_files = move_csv_files_to_processed_test(test_downloads_dir)
        
        print(f"Function returned {len(moved_files)} moved files:")
        for file_path in moved_files:
            print(f"  - {os.path.basename(file_path)}")
        
        # Verify results
        print(f"\nVerifying results...")
        
        # Check that downloads directory is empty of CSV files
        remaining_csv = [f for f in os.listdir(test_downloads_dir) 
                        if f.endswith('.csv') and os.path.isfile(os.path.join(test_downloads_dir, f))]
        
        if remaining_csv:
            print(f"❌ ERROR: {len(remaining_csv)} CSV files still in downloads directory:")
            for f in remaining_csv:
                print(f"  - {f}")
        else:
            print(f"✅ SUCCESS: Downloads directory is clean of CSV files")
        
        # Check that processed directory has the files
        if os.path.exists(test_processed_dir):
            processed_files = [f for f in os.listdir(test_processed_dir) if f.endswith('.csv')]
            print(f"✅ SUCCESS: Found {len(processed_files)} CSV files in processed directory:")
            for f in processed_files:
                print(f"  - {f}")
        else:
            print(f"❌ ERROR: Processed directory does not exist")
        
        # Test with existing files (conflict resolution)
        print(f"\nTesting conflict resolution...")
        
        # Create another file with the same name
        conflict_file = os.path.join(test_downloads_dir, "test-file-1.csv")
        with open(conflict_file, 'w') as f:
            f.write("NO.,Title,Price\n1,Conflict Test,999\n")
        print(f"Created conflict file: test-file-1.csv")
        
        # Run move function again
        moved_files_2 = move_csv_files_to_processed_test(test_downloads_dir)
        print(f"Second run moved {len(moved_files_2)} files")
        
        # Check processed directory again
        processed_files_final = [f for f in os.listdir(test_processed_dir) if f.endswith('.csv')]
        print(f"✅ Final processed directory has {len(processed_files_final)} CSV files:")
        for f in processed_files_final:
            print(f"  - {f}")
        
        print(f"\n🎉 Test completed successfully!")
        
    except Exception as e:
        print(f"❌ ERROR during testing: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up test directory
        if os.path.exists(test_downloads_dir):
            shutil.rmtree(test_downloads_dir)
            print(f"\nCleaned up test directory: {test_downloads_dir}")

if __name__ == "__main__":
    test_functionality()
