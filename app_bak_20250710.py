from fastapi import FastAP<PERSON>, HTTPException, Security, Depends, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from pydantic import BaseModel, SecretStr, field_validator
from typing import List, Optional, Dict, Any
import asyncio
import os
from datetime import datetime, UTC, timezone, timedelta
import pandas as pd
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from browser_use import Agent, Browser, BrowserConfig
from browser_use.browser.context import BrowserContextConfig
import tempfile
import shutil
import glob
import json
import logging
import secrets
import multiprocessing
import uvicorn
import uuid
from pydantic import ConfigDict
from prompts import QUERY_PARSER_PROMPT, BROWSER_SEARCH_PROMPT, SESSION_SUMMARY_PROMPT, format_query_parser_prompt, MIX_CATEGORY_PROMPT, KEYWORD_SEARCH_PROMPT, COMPARE_PRODUCT_TITLE
import traceback
from fastapi.responses import J<PERSON><PERSON><PERSON>ponse
from fastapi.exceptions import RequestValidationError
import re
import random
import string
import os.path
import base64
from pathlib import Path

def category_cleaner(category1, category2, category3):
    """
    Returns the last valid category from the given category hierarchy.
    A category is considered valid if it is not "All".
    
    Args:
        category1 (str): First level category
        category2 (str): Second level category
        category3 (str): Third level category
        
    Returns:
        str: The last valid category in the hierarchy
    """
    if category3 != "All":
        return category3
    elif category2 != "All":
        return category2
    else:
        return category1

# Configure logging with more detail
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# API Key security
API_KEY_NAME = "X-API-Key"
api_key_header = APIKeyHeader(name=API_KEY_NAME, auto_error=True)

async def get_api_key(api_key_header: str = Security(api_key_header)) -> str:
    if api_key_header == os.getenv("API_KEY"):
        return api_key_header
    raise HTTPException(
        status_code=401,
        detail="Invalid or missing API Key"
    )

# Initialize FastAPI app
app = FastAPI(title="Seerfar Product Search API")

# Global variables to track the current operations
current_browser = None
current_agent = None
operation_stopped = False

# Test mode configuration
TEST_MODE = False  # Set to True to bypass download process
TEST_CSV_PATH = "/Users/<USER>/downloads/Seerfar-Product20250404_6.csv"  # Path to your test CSV file

# Create screenshots directory
SCREENSHOTS_DIR = os.path.join(os.path.expanduser('~'), 'screenshots')
os.makedirs(SCREENSHOTS_DIR, exist_ok=True)

def get_screenshot_path(process_id: str, step_number: int) -> str:
    """Generate a unique screenshot path for a given process and step."""
    timestamp = datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')
    return os.path.join(SCREENSHOTS_DIR, f'{process_id}_step_{step_number}_{timestamp}.png')

# Pydantic models for request and response
class TextQueryRequest(BaseModel):
    query: str
    from_selection: bool = False
    category_1_item: Optional[List[str]] = None
    category_3_item: Optional[List[str]] = None
    
    @field_validator('query')
    def validate_query(cls, v):
        if not v or not isinstance(v, str):
            raise ValueError('Query must be a non-empty string')
        return v.strip()

class ProductSearchRequest(BaseModel):
    keyword: str
    min_price: Optional[float] = None
    max_price: Optional[float] = None
    record_count: int
    seller_type: str = "All"  # Default to "All" if not specified

class SalesInfo(BaseModel):
    count: int
    revenue: float
    growth_rate: float
    revenue_rate: float
    gross_margin: float

class PerformanceInfo(BaseModel):
    impressions: int
    product_card_views: int
    add_to_cart_rate: float
    order_conversion_rate: float
    advertising_cost_share: float
    return_cancellation_rate: float

class ProductDetails(BaseModel):
    variations: int
    ratings_count: int
    ratings_average: float
    launch_age: int
    weight: str
    volume: str

class SellerInfo(BaseModel):
    shop: str
    seller_type: str
    fulfillment: str

class Product(BaseModel):
    id: str
    image_url: str
    title: str
    listing_url: str
    sku: str
    brand: str
    categories: List[str]
    sales_method: str
    price: float
    sales: SalesInfo
    performance: PerformanceInfo
    product_details: ProductDetails
    seller_info: SellerInfo
    qa_count: int

class ProductSearchResponse(BaseModel):
    total_items: int
    timestamp: str
    products: List[Product]

# Process status models
class ProcessStatus(BaseModel):
    process_id: str
    status: str  # "running", "completed", "error", "stopped"
    start_time: datetime
    end_time: Optional[datetime] = None
    error: Optional[str] = None
    result: Optional[ProductSearchResponse] = None
    browser: Optional[Any] = None  # Using Any instead of Browser
    agent: Optional[Any] = None    # Using Any instead of Agent

    model_config = ConfigDict(arbitrary_types_allowed=True)

class ProcessInfo(BaseModel):
    process_id: str
    status: str
    start_time: datetime
    end_time: Optional[datetime] = None
    error: Optional[str] = None

class GetResponseResult(BaseModel):
    status_code: int
    message: str
    data: Optional[ProductSearchResponse] = None

# Session Summary models
class SessionSummaryResponse(BaseModel):
    status_code: int
    message: str
    query: str
    summary: str

class ProductCheckRequest(BaseModel):
    title_1688: str
    title_ozon: str

class ProductCheckResponse(BaseModel):
    is_match: bool

def setup_process_logging(process_id: str) -> logging.Logger:
    """Set up logging for a specific process."""
    # Create logs directory in /tmp which is typically writable
    logs_dir = os.path.join('/tmp', 'seerfar_logs')
    os.makedirs(logs_dir, exist_ok=True)
    
    # Create a logger for this process
    process_logger = logging.getLogger(f'process_{process_id}')
    process_logger.setLevel(logging.INFO)  
    
    # Remove any existing handlers to avoid duplicate logs
    for handler in process_logger.handlers[:]:
        process_logger.removeHandler(handler)
    
    # Get current date and time for the filename
    current_time = datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')
    
    # Create a file handler for this process with date and time at the beginning of filename
    log_file = os.path.join(logs_dir, f'{current_time}_{process_id}.log')
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(logging.INFO) 
    
    # Create a formatter with more detailed information
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - [%(process)d:%(thread)d] - %(message)s'
    )
    file_handler.setFormatter(formatter)
    
    # Add the handler to the logger
    process_logger.addHandler(file_handler)
    
    # Also capture browser_use service logs
    browser_use_logger = logging.getLogger('browser_use')
    browser_use_logger.setLevel(logging.INFO)
    browser_use_logger.addHandler(file_handler)
    
    # Ensure logs are flushed immediately
    file_handler.flush()
    
    return process_logger

# Process manager to handle multiple processes
class ProcessManager:
    def __init__(self):
        self.processes: Dict[str, ProcessStatus] = {}
        self.lock = asyncio.Lock()

    async def create_process(self) -> str:
        async with self.lock:
            process_id = str(uuid.uuid4())
            # Set up process-specific logging
            process_logger = setup_process_logging(process_id)
            process_logger.info(f"Process {process_id} created")
            
            self.processes[process_id] = ProcessStatus(
                process_id=process_id,
                status="running",
                start_time=datetime.now(timezone.utc)
            )
            return process_id

    async def update_process(self, process_id: str, status: str, error: Optional[str] = None, result: Optional[ProductSearchResponse] = None, browser: Optional[Browser] = None, agent: Optional[Agent] = None):
        async with self.lock:
            if process_id in self.processes:
                process = self.processes[process_id]
                process.status = status
                if error:
                    process.error = error
                if result:
                    process.result = result
                if browser:
                    process.browser = browser
                if agent:
                    process.agent = agent
                if status in ["completed", "error", "stopped"]:
                    process.end_time = datetime.now(timezone.utc)
                
                # Log the status update
                process_logger = logging.getLogger(f'process_{process_id}')
                process_logger.info(f"Process status updated to: {status}")
                if error:
                    process_logger.error(f"Process error: {error}")

    async def get_process(self, process_id: str) -> Optional[ProcessStatus]:
        async with self.lock:
            return self.processes.get(process_id)

    async def stop_process(self, process_id: str) -> bool:
        async with self.lock:
            if process_id in self.processes:
                process = self.processes[process_id]
                if process.status == "running":
                    # Stop the agent if it exists
                    if process.agent:
                        process.agent.stop()
                        process.agent = None
                    
                    # Close the browser if it exists
                    if process.browser:
                        await process.browser.close()
                        process.browser = None
                    
                    process.status = "stopped"
                    process.end_time = datetime.now(timezone.utc)
                    return True
        return False

# Initialize process manager
process_manager = ProcessManager()

@app.post("/stop/{process_id}")
async def stop_process(process_id: str, api_key: str = Depends(get_api_key)):
    success = await process_manager.stop_process(process_id)
    if not success:
        raise HTTPException(
            status_code=404,
            detail=f"Process {process_id} not found or already stopped"
        )
    return {"message": f"Process {process_id} stopped successfully"}

@app.get("/get_response/{process_id}", response_model=GetResponseResult)
async def get_response(process_id: str, api_key: str = Depends(get_api_key)):
    try:
        logger.info(f"Getting response for process ID: {process_id}")
        process = await process_manager.get_process(process_id)
        
        if not process:
            logger.warning(f"Process {process_id} not found")
            return GetResponseResult(
                status_code=404,
                message=f"Process {process_id} not found"
            )
        
        logger.info(f"Process status: {process.status}")
        
        if process.status == "running":
            return GetResponseResult(
                status_code=202,
                message="Process is still running"
            )
        
        if process.status == "error":
            logger.error(f"Process error: {process.error}")
            return GetResponseResult(
                status_code=500,
                message=process.error or "An error occurred during processing"
            )
        
        if process.status == "stopped":
            return GetResponseResult(
                status_code=499,
                message="Process was stopped by user"
            )
        
        if process.status == "completed" and process.result:
            logger.info("Process completed successfully")
            return GetResponseResult(
                status_code=200,
                message="Process completed successfully",
                data=process.result
            )
        
        logger.error("Unexpected process state")
        return GetResponseResult(
            status_code=500,
            message="Unexpected process state"
        )
    except Exception as e:
        logger.error(f"Error in get_response: {str(e)}\n{traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

async def run_browser_search(username: str, password: str, request: ProductSearchRequest):
    if TEST_MODE:
        return TEST_CSV_PATH
        
    # Configure browser context with cookies
    downloads_path = os.path.join(os.path.expanduser('~'), 'downloads')
    os.makedirs(downloads_path, exist_ok=True)  # Ensure downloads directory exists
    
    # Get current timestamp before starting the agent
    start_time = datetime.now(timezone.utc)
    
    # Simplified browser configuration
    context_config = BrowserContextConfig(
        cookies_file='cookies/seerfar.json',
        disable_security=True,
        wait_for_network_idle_page_load_time=2.0,
        minimum_wait_page_load_time=1.0,
        maximum_wait_page_load_time=10.0,
        wait_between_actions=1.0,
        save_downloads_path=downloads_path,
        user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.102 Safari/537.36'
    )
    
    # Configure browser
    browser_config = BrowserConfig(
        headless=True,
        disable_security=True,
        new_context_config=context_config
    )
    
    # Create browser instance
    browser = Browser(config=browser_config)
    agent = None
    
    try:
        agent = Agent(
            task=BROWSER_SEARCH_PROMPT.format(
                username=username,
                password=password,
                min_price=request.min_price,
                max_price=request.max_price,
                category1=request.category1,
                category2=request.category2,
                category3=request.category3,
                seller_type=request.seller_type,
                last_valid_category=request.last_valid_category
            ),
            llm=ChatOpenAI(
                base_url='https://openrouter.ai/api/v1',
                #model='openai/gpt-4o-2024-11-20',
                model='openai/gpt-4.1',
                #model='openai/gpt-4.1-mini',
                temperature=0,
                api_key=SecretStr(str(os.getenv("OPENROUTER_API_KEY"))),
            ),
            use_vision=True,
            browser=browser
        )

        await agent.run()
            
        # Find CSV files created after the start time
        logger.info("Looking for downloaded CSV file")
        csv_files = []
        for file in glob.glob(os.path.join(downloads_path, "Seerfar-Product*.csv")):
            file_time = datetime.fromtimestamp(os.path.getctime(file), timezone.utc)
            if file_time >= start_time:
                csv_files.append(file)
        
        if not csv_files:
            raise Exception("No Seerfar CSV file found in downloads directory that was created during this run")
        
        # Get the most recent file from the filtered list
        latest_file = max(csv_files, key=os.path.getctime)
        logger.info(f"Found CSV file: {latest_file}")
        return latest_file
    except Exception as e:
        raise e
    finally:
        if agent:
            agent.stop()
        if browser:
            await browser.close()

def process_csv_to_json(csv_path: str) -> ProductSearchResponse:
    try:
        # Read the CSV file
        df = pd.read_csv(csv_path)
        
        # Print column names for debugging
        print("CSV Columns:", df.columns.tolist())
        
        # Helper function to safely convert to integer
        def safe_int(value):
            if pd.isna(value):
                return 0
            try:
                return int(float(value))
            except (ValueError, TypeError):
                return 0
        
        # Convert DataFrame to list of Product objects
        products = []
        for index, row in df.iterrows():
            try:
                # Clean up price and revenue values by removing currency symbol and converting to float
                price = float(str(row['Price']).replace('₽', '').strip())
                revenue = float(str(row['Revenue']).replace('₽', '').strip())
                
                # Clean up percentage values by removing % symbol and converting to float
                def clean_percentage(value):
                    if pd.isna(value):
                        return 0.0
                    return float(str(value).replace('%', '').strip()) / 100
                
                # Extract launch age in months
                launch_age_parts = str(row['Launch Age']).split('\n')
                if len(launch_age_parts) > 1:
                    # Extract age from the second part
                    age_parts = launch_age_parts[1].split()
                    if len(age_parts) >= 2:
                        if 'Years' in age_parts[1]:
                            years = int(age_parts[0])
                            months = int(age_parts[2]) if len(age_parts) > 3 else 0
                            launch_age_months = years * 12 + months
                        elif 'Months' in age_parts[1]:
                            launch_age_months = int(age_parts[0])
                        else:
                            launch_age_months = 0
                    else:
                        launch_age_months = 0
                else:
                    launch_age_months = 0
                
                # Handle NaN values in Brand and other fields
                brand = str(row['Brand']) if pd.notna(row['Brand']) else ""
                weight = str(row['Weight']) if pd.notna(row['Weight']) else "N/A"
                volume = str(row['Volume']) if pd.notna(row['Volume']) else "N/A"
                
                product = Product(
                    id=str(row['SKU']),  # Using SKU as the ID
                    image_url=row['Image'],
                    title=row['Title'],
                    listing_url=row['Listing URL'],
                    sku=str(row['SKU']),
                    brand=brand,
                    categories=[cat.strip() for cat in str(row['Categories']).split('\n')],
                    sales_method=str(row['sales method']),
                    price=price,
                    sales=SalesInfo(
                        count=safe_int(row['Sales']),
                        revenue=revenue,
                        growth_rate=clean_percentage(row['Sales Growth Rate']),
                        revenue_rate=clean_percentage(row['Revenue Rate']),
                        gross_margin=clean_percentage(row['Gross Margin'])
                    ),
                    performance=PerformanceInfo(
                        impressions=safe_int(row['Impressions']),
                        product_card_views=safe_int(row['Product Card Views']),
                        add_to_cart_rate=clean_percentage(row['Add-to-Cart Rate']),
                        order_conversion_rate=clean_percentage(row['Order conversion rate']),
                        advertising_cost_share=clean_percentage(row['Advertising cost share']),
                        return_cancellation_rate=clean_percentage(row['Return cancellation rate'])
                    ),
                    product_details=ProductDetails(
                        variations=safe_int(row['Variations']),
                        ratings_count=safe_int(row['NO. Ratings']),
                        ratings_average=float(row['Ratings']) if pd.notna(row['Ratings']) else 0.0,
                        launch_age=launch_age_months,  # Now using months as the unit
                        weight=weight,
                        volume=volume
                    ),
                    seller_info=SellerInfo(
                        shop=str(row['Shop']) if pd.notna(row['Shop']) else "",
                        seller_type=str(row['Seller type']) if pd.notna(row['Seller type']) else "",
                        fulfillment=str(row['Fulfillment']) if pd.notna(row['Fulfillment']) else ""
                    ),
                    qa_count=safe_int(row['Q&A'])
                )
                products.append(product)
            except Exception as e:
                print(f"Error processing row {index}: {e}")
                continue  # Skip this row and continue with the next one
        
        if not products:
            raise Exception("No valid products found in the CSV file")
            
        return ProductSearchResponse(
            total_items=len(products),
            timestamp=datetime.now(timezone(timedelta(hours=8))).isoformat(),
            products=products
        )
    except Exception as e:
        raise Exception(f"Error processing CSV file: {str(e)}")

async def parse_query_with_llm(query: str, from_selection: bool = False) -> ProductSearchRequest:
    """Parse the text query using LLM to extract search parameters."""
    llm = ChatOpenAI(
        base_url='https://openrouter.ai/api/v1',
        #model='google/gemini-2.0-flash-001',
        model='google/gemini-2.0-flash-lite-001',
        temperature=0,
        api_key=SecretStr(str(os.getenv("OPENROUTER_API_KEY"))),
    )
    
    try:
        # Format the prompt with the query
        formatted_prompt = format_query_parser_prompt(query)
        
        # Get response from LLM
        response = await llm.ainvoke(formatted_prompt)
        
        # Convert response to string and clean it
        if isinstance(response.content, (list, dict)):
            content = json.dumps(response.content)
        else:
            content = str(response.content).strip()
        
        logger.info(f"Raw LLM Response: {content}")
        
        # Clean and validate JSON
        try:
            # Extract JSON object if response contains extra text
            import re
            json_match = re.search(r'\{[^{}]*\}', content)
            if json_match:
                content = json_match.group()
            
            logger.info(f"Cleaned content: {content}")
            parsed_data = json.loads(content)
        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error: {str(e)}")
            logger.error(f"Problematic JSON string: {content}")
            raise ValueError(f"Invalid JSON format: {str(e)}")
        
        logger.info(f"Parsed JSON: {json.dumps(parsed_data, indent=2)}")
        
        # Validate required fields and types
        required_fields = {
            'keyword': str,
            'min_price': (int, float),
            'max_price': (int, float),
            'record_count': int,
            'seller_type': str
        }
        
        # Clean and validate each field
        for field, expected_type in required_fields.items():
            # Handle missing fields
            if field not in parsed_data:
                if field == 'seller_type':
                    parsed_data[field] = "All"
                elif field == 'keyword':
                    parsed_data[field] = query  # Use the original query as keyword if not provided
                elif field in ['min_price', 'max_price']:
                    parsed_data[field] = None
                else:
                    parsed_data[field] = 0
                continue
                
            value = parsed_data[field]
            
            # Clean string values
            if expected_type == str:
                # Ensure proper string formatting
                if not isinstance(value, str):
                    value = str(value)
                # Remove any invalid characters and ensure proper quotes
                value = value.strip().replace('\n', ' ').replace('\r', '')
                if value == "":
                    if field == 'seller_type':
                        value = "All"
                    elif field == 'keyword':
                        value = query  # Use the original query as keyword if empty
                    else:
                        value = "0"
                parsed_data[field] = value
            
            # Handle numeric values
            elif expected_type in [(int, float), int]:
                try:
                    if isinstance(value, str):
                        value = value.strip().replace(',', '').replace(' ', '')
                        if value.lower() == 'none' or value == '':
                            parsed_data[field] = None
                            continue
                    value = float(value)
                    if expected_type == int:
                        value = int(value)
                    parsed_data[field] = value
                except (ValueError, TypeError):
                    parsed_data[field] = None
        
        # Ensure numeric fields are non-negative if they are not None
        for field in ['min_price', 'max_price', 'record_count']:
            if parsed_data[field] is not None:
                parsed_data[field] = max(0, float(parsed_data[field]))
        
        # Ensure min_price <= max_price if both are not None
        if parsed_data['min_price'] is not None and parsed_data['max_price'] is not None:
            if parsed_data['min_price'] > parsed_data['max_price']:
                parsed_data['min_price'], parsed_data['max_price'] = parsed_data['max_price'], parsed_data['min_price']
        
        # Validate seller_type
        valid_seller_types = ["All", "Cross-border sellers", "Domestic seller"]
        if parsed_data['seller_type'] not in valid_seller_types:
            parsed_data['seller_type'] = "All"
        
        # If from_selection is True, we don't need to parse categories
        if not from_selection:
            # No need to handle categories anymore since we're using keyword
            pass
        else:
            # Set default values for categories when from_selection is True
            parsed_data['category1'] = "All"
            parsed_data['category2'] = "All"
            parsed_data['category3'] = "All"
            parsed_data['last_valid_category'] = "All"
        
        logger.info(f"Validated parameters: {json.dumps(parsed_data, indent=2)}")
        
        return ProductSearchRequest(**parsed_data)
    except Exception as e:
        logger.error(f"Error parsing query: {str(e)}")
        logger.error(f"Stack trace: {traceback.format_exc()}")
        raise HTTPException(
            status_code=400,
            detail=f"Failed to parse query: {str(e)}"
        )

@app.post("/search", response_model=ProcessInfo)
async def search_products(
    request: Request,
    text_query: TextQueryRequest,
    api_key: str = Depends(get_api_key)
):
    try:
        # Log the incoming request
        body = await request.body()
        logger.info(f"Received search request: {body.decode()}")
        
        # Validate the query
        query = text_query.query
        logger.info(f"Validated query: {query}")
        
        username = os.getenv("SEERFAR_USERNAME")
        password = os.getenv("SEERFAR_PASSWORD")
        
        if not username or not password:
            logger.error("SEERFAR credentials not configured")
            raise HTTPException(status_code=500, detail="SEERFAR credentials not configured")
        
        # Create a new process
        process_id = await process_manager.create_process()
        logger.info(f"Created new process with ID: {process_id}")
        
        # Start the search process in the background
        asyncio.create_task(_run_search_process(process_id, username, password, text_query))
        logger.info(f"Started background search process for ID: {process_id}")
        
        # Return process information immediately
        process = await process_manager.get_process(process_id)
        logger.info(f"Returning process info for ID: {process_id}")
        return ProcessInfo(
            process_id=process.process_id,
            status=process.status,
            start_time=process.start_time
        )
    except Exception as e:
        logger.error(f"Error in search_products: {str(e)}\n{traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

def translate_categories_to_english(chinese_categories: List[str]) -> List[str]:
    """
    Translate Chinese categories to English using category_cleaned.json.
    Only return a list with the first exactly matched category translation, or an empty list if no exact match is found.
    Args:
        chinese_categories (List[str]): List of Chinese category names or paths
    Returns:
        List[str]: List with one translated English category name (if exactly matched), or empty list
    """
    try:
        # Load the category mapping from JSON file
        json_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'category_cleaned.json')
        logger.info(f"Loading category mapping from: {json_path}")
        with open(json_path, 'r', encoding='utf-8') as f:
            category_data = json.load(f)
        def extract_categories(category_list):
            categories = []
            for category in category_list:
                if isinstance(category, dict):
                    if 'cnTitle' in category and 'enTitle' in category:
                        categories.append((category['cnTitle'], category['enTitle']))
                    if 'children' in category:
                        categories.extend(extract_categories(category['children']))
            return categories
        all_categories = extract_categories(category_data.get('oz', []))
        cn_to_en = dict(all_categories)
        logger.info(f"Created mapping with {len(cn_to_en)} Chinese to English translations")
        # Try to find the first exact match
        for category in chinese_categories:
            logger.info(f"Translating category: {category}")
            # Handle category paths (e.g., "家电/技术秤")
            if '/' in category:
                last_category = category.split('/')[-1]
            else:
                last_category = category
            if last_category in cn_to_en:
                translated = cn_to_en[last_category]
                logger.info(f"Found translation for last category: {last_category} -> {translated}")
                return [translated]
        # No exact match found
        logger.warning("No exact match found for any category.")
        return []
    except Exception as e:
        logger.error(f"Error translating categories: {str(e)}")
        logger.error(f"Stack trace: {traceback.format_exc()}")
        return []  # Return empty list if translation fails

async def check_successful_download(browser_use_logger) -> bool:
    """Check if the browser_use logger contains the successful download message and no failure message."""
    success_pattern = 'Downloaded file to /home/<USER>/downloads'
    failure_patterns = [
        'Unfinished',
        'Error in agent execution',
        'No successful file download detected in logs',
        'No Seerfar CSV file found in downloads directory',
        # Add any other error/failure patterns you want to catch
    ]
    for handler in browser_use_logger.handlers:
        if isinstance(handler, logging.FileHandler):
            try:
                with open(handler.baseFilename, 'r') as f:
                    log_content = f.read()
                    # Only return True if success pattern is present and no failure pattern
                    if success_pattern in log_content:
                        return True
                        
                    # If any failure pattern is present, return False immediately
                    if any(pattern in log_content for pattern in failure_patterns):
                        return False
                    
            except Exception as e:
                logger.error(f"Error reading log file: {e}")
    return False

async def _run_search_process(process_id: str, username: str, password: str, text_query: TextQueryRequest):
    try:
        process_logger = logging.getLogger(f'process_{process_id}')
        browser_use_logger = logging.getLogger('browser_use')
        
        logger.info(f"Starting search process for ID: {process_id}")
        process_logger.info(f"Starting search process for ID: {process_id}")
        browser_use_logger.info(f"Starting search process for ID: {process_id}")
        
        # Parse the text query into structured parameters
        logger.info("Parsing query with LLM")
        process_logger.info("Parsing query with LLM")
        browser_use_logger.info("Parsing query with LLM")
        
        if text_query.from_selection:
            # Translate categories to English
            translated_category_1_items = translate_categories_to_english(text_query.category_1_item or [])
            translated_category_3_items = translate_categories_to_english(text_query.category_3_item or [])
            
            logger.info(f"Translated category_1_items: {translated_category_1_items}")
            logger.info(f"Translated category_3_items: {translated_category_3_items}")
            
            # Use MIX_CATEGORY_PROMPT for selection-based search
            search_params = await parse_query_with_llm(text_query.query, True)
            logger.info(f"Parsed search parameters: {search_params}")
            process_logger.info(f"Parsed search parameters: {search_params}")
            browser_use_logger.info(f"Parsed search parameters: {search_params}")
        else:
            # Use KEYWORD_SEARCH_PROMPT for keyword-based search
            search_params = await parse_query_with_llm(text_query.query, False)
            logger.info(f"Parsed search parameters: {search_params}")
            process_logger.info(f"Parsed search parameters: {search_params}")
            browser_use_logger.info(f"Parsed search parameters: {search_params}")
        
        # Configure browser context with cookies
        downloads_path = os.path.join(os.path.expanduser('~'), 'downloads')
        os.makedirs(downloads_path, exist_ok=True)  # Ensure downloads directory exists
        
        logger.info("Configuring browser")
        process_logger.info("Configuring browser")
        browser_use_logger.info("Configuring browser")
        
        # Set user agent string
        user_agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.102 Safari/537.36'
        
        # Create a process-specific screenshots directory
        process_screenshots_dir = os.path.join(SCREENSHOTS_DIR, process_id)
        os.makedirs(process_screenshots_dir, exist_ok=True)
        
        context_config = BrowserContextConfig(
            cookies_file='cookies/seerfar.json',
            disable_security=True,
            save_downloads_path=downloads_path,
            user_agent=user_agent,
            trace_path=process_screenshots_dir  # Enable tracing which includes screenshots
        )
        
        browser_config = BrowserConfig(
            headless=True,
            disable_security=True,
            new_context_config=context_config
        )
        
        # Create browser instance
        logger.info("Creating browser instance")
        process_logger.info("Creating browser instance")
        browser_use_logger.info("Creating browser instance")
        browser = Browser(config=browser_config)
        agent = None
        
        try:
            logger.info("Creating agent")
            process_logger.info("Creating agent")
            browser_use_logger.info("Creating agent")
            
            # Choose the appropriate prompt based on from_selection
            if text_query.from_selection:
                prompt = MIX_CATEGORY_PROMPT.format(
                    username=username,
                    password=password,
                    min_price=search_params.min_price,
                    max_price=search_params.max_price,
                    category_1_items=translated_category_1_items,
                    category_3_items=translated_category_3_items,
                    seller_type=search_params.seller_type
                )
            else:
                prompt = KEYWORD_SEARCH_PROMPT.format(
                    username=username,
                    password=password,
                    keyword=search_params.keyword,
                    seller_type=search_params.seller_type
                )
            
            agent = Agent(
                task=prompt,
                llm=ChatOpenAI(
                    base_url='https://openrouter.ai/api/v1',
                    model='openai/gpt-4.1',
                    #model='openai/gpt-4.1-mini',
                    temperature=0,
                    api_key=SecretStr(str(os.getenv("OPENROUTER_API_KEY"))),
                ),
                use_vision=True,
                browser=browser
            )
            
            # Update process with browser and agent instances
            await process_manager.update_process(process_id, "running", browser=browser, agent=agent)
            
            logger.info("Running agent")
            process_logger.info("Running agent")
            browser_use_logger.info("Running agent")
            await agent.run()
            
            # Check if process was stopped
            process = await process_manager.get_process(process_id)
            if process and process.status == "stopped":
                logger.info("Process was stopped")
                process_logger.info("Process was stopped")
                browser_use_logger.info("Process was stopped")
                return

            # Check for successful download log
            if not await check_successful_download(browser_use_logger):
                error_msg = "No successful file download detected in logs"
                logger.error(error_msg)
                process_logger.error(error_msg)
                browser_use_logger.error(error_msg)
                await process_manager.update_process(process_id, "error", error=error_msg)
                return
                
            # Find the most recently downloaded Seerfar CSV file
            logger.info("Looking for downloaded CSV file")
            process_logger.info("Looking for downloaded CSV file")
            browser_use_logger.info("Looking for downloaded CSV file")
            csv_files = glob.glob(os.path.join(downloads_path, "Seerfar-Product*.csv"))
            if not csv_files:
                error_msg = "No Seerfar CSV file found in downloads directory"
                logger.error(error_msg)
                process_logger.error(error_msg)
                browser_use_logger.error(error_msg)
                raise Exception(error_msg)
            
            # Get the most recent file
            latest_file = max(csv_files, key=os.path.getctime)
            logger.info(f"Found CSV file: {latest_file}")
            process_logger.info(f"Found CSV file: {latest_file}")
            browser_use_logger.info(f"Found CSV file: {latest_file}")
            
            # Process the downloaded CSV file
            logger.info("Processing CSV file")
            process_logger.info("Processing CSV file")
            browser_use_logger.info("Processing CSV file")
            response = process_csv_to_json(latest_file)
            
            # Update process with result
            logger.info("Updating process with result")
            process_logger.info("Updating process with result")
            browser_use_logger.info("Updating process with result")
            await process_manager.update_process(process_id, "completed", result=response)
            
        except Exception as e:
            error_message = str(e)
            logger.error(f"Error in agent execution: {error_message}\n{traceback.format_exc()}")
            process_logger.error(f"Error in agent execution: {error_message}\n{traceback.format_exc()}")
            browser_use_logger.error(f"Error in agent execution: {error_message}\n{traceback.format_exc()}")
            if "Operation was stopped by user" in error_message:
                await process_manager.update_process(process_id, "stopped", error=error_message)
            else:
                await process_manager.update_process(process_id, "error", error=error_message)
        finally:
            # Clean up resources
            logger.info("Cleaning up resources")
            process_logger.info("Cleaning up resources")
            browser_use_logger.info("Cleaning up resources")
            if agent:
                agent.stop()
            if browser:
                await browser.close()
            # Clear browser and agent references
            await process_manager.update_process(process_id, process.status, browser=None, agent=None)
            
            # Ensure all logs are flushed
            for handler in process_logger.handlers:
                handler.flush()
            for handler in browser_use_logger.handlers:
                handler.flush()
    except Exception as e:
        error_message = str(e)
        logger.error(f"Error in _run_search_process: {error_message}\n{traceback.format_exc()}")
        process_logger.error(f"Error in _run_search_process: {error_message}\n{traceback.format_exc()}")
        browser_use_logger.error(f"Error in _run_search_process: {error_message}\n{traceback.format_exc()}")
        await process_manager.update_process(process_id, "error", error=error_message)
        
        # Ensure all logs are flushed
        for handler in process_logger.handlers:
            handler.flush()
        for handler in browser_use_logger.handlers:
            handler.flush()

async def summarize_query_with_llm(query: str) -> str:
    """Summarize the text query using LLM to provide a simple text summary."""
    llm = ChatOpenAI(
        base_url='https://openrouter.ai/api/v1',
        #model='openai/gpt-4o-2024-11-20',
       # model='google/gemini-2.0-flash-lite-001',
        model='openai/gpt-4.1-nano',
        temperature=0,
        api_key=SecretStr(str(os.getenv("OPENROUTER_API_KEY"))),
    )
    
    prompt = SESSION_SUMMARY_PROMPT.format(query=query)
    
    try:
        response = await llm.ainvoke(prompt)
        # Get the response content as string
        summary = str(response.content).strip()
        logger.info(f"LLM Summary Response: {summary}")
        
        if not summary:
            raise ValueError("Empty summary received from LLM")
            
        return summary
    except Exception as e:
        logger.error(f"Error summarizing query: {str(e)}")
        raise HTTPException(status_code=400, detail=f"Failed to summarize query: {str(e)}")

@app.post("/session_summary", response_model=SessionSummaryResponse)
async def get_session_summary(
    request: TextQueryRequest,
    api_key: str = Depends(get_api_key)
):
    try:
        logger.info(f"Received summary request with query: {request.query}")
        summary = await summarize_query_with_llm(request.query)
        return SessionSummaryResponse(
            status_code=200,
            message="Summary generated successfully",
            query=request.query,
            summary=summary
        )
    except Exception as e:
        logger.error(f"Error in get_session_summary: {str(e)}\n{traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/product_check", response_model=ProductCheckResponse)
def product_check(
    request: ProductCheckRequest,
    api_key: str = Depends(get_api_key)
):
    try:
        llm = ChatOpenAI(
            base_url='https://openrouter.ai/api/v1',
            model='openai/gpt-4.1-mini',
            #model='openai/gpt-4.1-nano',
            temperature=0,
            api_key=SecretStr(str(os.getenv("OPENROUTER_API_KEY"))),
        )
        prompt = COMPARE_PRODUCT_TITLE.format(title1=request.title_1688, title2=request.title_ozon)
        response = llm.invoke(prompt)
        result = str(response.content).strip().lower()
        is_match = result == 'true'
        return ProductCheckResponse(is_match=is_match)
    except Exception as e:
        logger.error(f"Error in product_check: {str(e)}\n{traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.middleware("http")
async def validate_json_middleware(request: Request, call_next):
    if request.method in ["POST", "PUT", "PATCH"]:
        try:
            # Try to read and parse the body as JSON
            body = await request.body()
            if body:
                try:
                    body_str = body.decode()
                    logger.info(f"Received request body: {body_str}")
                    # Attempt to parse JSON to validate it
                    json.loads(body_str)
                except json.JSONDecodeError as e:
                    return JSONResponse(
                        status_code=400,
                        content={
                            "detail": [{
                                "type": "json_invalid",
                                "loc": ["body"],
                                "msg": str(e),
                                "input": body_str
                            }]
                        }
                    )
                except Exception as e:
                    logger.error(f"Error processing request body: {e}")
                    return JSONResponse(
                        status_code=400,
                        content={
                            "detail": [{
                                "type": "json_invalid",
                                "loc": ["body"],
                                "msg": "Invalid request body",
                                "input": str(e)
                            }]
                        }
                    )
        except Exception as e:
            logger.error(f"Error in JSON validation middleware: {e}")
    
    response = await call_next(request)
    return response

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000) 