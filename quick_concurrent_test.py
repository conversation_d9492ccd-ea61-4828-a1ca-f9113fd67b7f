#!/usr/bin/env python3
"""
Quick Concurrent Search Test

A simplified test to quickly verify that the /search API can handle concurrent requests.
This test sends 3 requests simultaneously and measures timing to detect concurrency.

Usage:
    python quick_concurrent_test.py

Set environment variables:
    export API_KEY="your-api-key"
    python quick_concurrent_test.py
"""

import asyncio
import aiohttp
import time
import os
import sys

API_BASE_URL = "http://localhost:8000"
API_KEY = os.getenv("API_KEY", "test-api-key")

# Simple test queries
QUERIES = [
    "Find wireless headphones under $50",
    "Search for laptop bags",
    "Look for phone chargers"
]

async def send_request(session, query, test_id):
    """Send a single search request"""
    start_time = time.time()
    
    try:
        async with session.post(
            f"{API_BASE_URL}/search",
            json={"query": query},
            headers={"X-API-Key": API_KEY}
        ) as response:
            duration = time.time() - start_time
            
            if response.status == 200:
                data = await response.json()
                print(f"✅ Request {test_id}: {duration:.2f}s - Process ID: {data.get('process_id')}")
                return {"success": True, "duration": duration, "process_id": data.get('process_id')}
            else:
                error = await response.text()
                print(f"❌ Request {test_id}: {duration:.2f}s - Error: {response.status}")
                return {"success": False, "duration": duration, "error": error}
                
    except Exception as e:
        duration = time.time() - start_time
        print(f"❌ Request {test_id}: {duration:.2f}s - Exception: {str(e)}")
        return {"success": False, "duration": duration, "error": str(e)}

async def test_concurrent_requests():
    """Test concurrent request handling"""
    print("🚀 Testing Concurrent Search API...")
    print(f"API URL: {API_BASE_URL}")
    print(f"Sending {len(QUERIES)} concurrent requests...\n")
    
    start_time = time.time()
    
    async with aiohttp.ClientSession() as session:
        # Send all requests concurrently
        tasks = [
            send_request(session, query, i+1) 
            for i, query in enumerate(QUERIES)
        ]
        
        results = await asyncio.gather(*tasks)
    
    total_time = time.time() - start_time
    
    # Analyze results
    successful = [r for r in results if r["success"]]
    failed = [r for r in results if not r["success"]]
    
    print(f"\n📊 Results Summary:")
    print(f"Total time: {total_time:.2f} seconds")
    print(f"Successful requests: {len(successful)}/{len(QUERIES)}")
    print(f"Failed requests: {len(failed)}")
    
    if successful:
        avg_duration = sum(r["duration"] for r in successful) / len(successful)
        max_duration = max(r["duration"] for r in successful)
        
        print(f"Average request time: {avg_duration:.2f}s")
        print(f"Longest request time: {max_duration:.2f}s")
        
        # Check for concurrency
        # If requests were sequential, total time would be sum of individual times
        # If concurrent, total time should be close to the longest individual request
        sequential_estimate = sum(r["duration"] for r in successful)
        concurrency_ratio = sequential_estimate / total_time
        
        print(f"Sequential time estimate: {sequential_estimate:.2f}s")
        print(f"Concurrency ratio: {concurrency_ratio:.1f}x")
        
        if concurrency_ratio > 1.5:
            print("✅ CONCURRENT EXECUTION DETECTED! 🎉")
            print(f"   Requests processed ~{concurrency_ratio:.1f}x faster than sequential")
        else:
            print("⚠️  Sequential execution detected")
            print("   Requests may be blocking each other")
    
    if failed:
        print(f"\n❌ Failed requests:")
        for i, result in enumerate(failed):
            print(f"   Request {i+1}: {result.get('error', 'Unknown error')}")
    
    return len(successful) > 0 and (len(successful) == 0 or sequential_estimate / total_time > 1.5)

async def main():
    """Main function"""
    try:
        success = await test_concurrent_requests()
        
        if success:
            print("\n🎉 Test completed successfully!")
            print("The API appears to be handling requests concurrently.")
        else:
            print("\n⚠️  Test completed with issues.")
            print("The API may not be processing requests concurrently.")
            
        return success
        
    except Exception as e:
        print(f"\n💥 Test failed: {str(e)}")
        return False

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--help":
        print(__doc__)
        sys.exit(0)
    
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted")
        sys.exit(1)
