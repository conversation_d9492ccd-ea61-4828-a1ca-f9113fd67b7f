#!/usr/bin/env python3
"""
Test script to verify the new timestamp-processID folder structure works correctly.

This script tests:
1. PathManager functionality
2. Session folder creation
3. File organization
4. Path resolution
5. Integration with existing utilities
"""

import os
import tempfile
import shutil
import uuid
import time
from datetime import datetime, timezone
from path_manager import PathManager, initialize_session_paths, get_path_manager

def test_path_manager_basic():
    """Test basic PathManager functionality"""
    print("=== Testing PathManager Basic Functionality ===")
    
    # Create a temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        # Initialize PathManager with test directory
        path_manager = PathManager(base_downloads_dir=temp_dir)
        
        # Test session folder creation
        process_id = str(uuid.uuid4())
        session_folder = path_manager.create_session_folder(process_id)
        
        print(f"✓ Created session folder: {session_folder}")
        assert os.path.exists(session_folder), "Session folder should exist"
        
        # Verify folder name format
        folder_name = os.path.basename(session_folder)
        assert PathManager.is_session_folder(folder_name), "Folder should follow timestamp-processID pattern"
        print(f"✓ Folder name follows correct pattern: {folder_name}")
        
        # Test path getters
        downloads_path = path_manager.get_downloads_path()
        processed_path = path_manager.get_processed_path()
        
        assert downloads_path == session_folder, "Downloads path should be session folder"
        assert processed_path == os.path.join(session_folder, "processed"), "Processed path should be session/processed"
        assert os.path.exists(processed_path), "Processed directory should be created"
        
        print(f"✓ Downloads path: {downloads_path}")
        print(f"✓ Processed path: {processed_path}")
        
        # Test process ID and timestamp retrieval
        assert path_manager.get_process_id() == process_id, "Process ID should match"
        assert path_manager.get_timestamp() is not None, "Timestamp should be set"
        
        print(f"✓ Process ID: {path_manager.get_process_id()}")
        print(f"✓ Timestamp: {path_manager.get_timestamp()}")

def test_session_folder_pattern():
    """Test session folder pattern recognition"""
    print("\n=== Testing Session Folder Pattern Recognition ===")
    
    # Valid patterns
    valid_patterns = [
        "20250120_143022-abc123",
        "20250120_143022-12345678-1234-1234-1234-123456789012",
        "20250120_143022-process-id-with-dashes"
    ]
    
    # Invalid patterns
    invalid_patterns = [
        "20250120-abc123",  # Missing time
        "2025012_143022-abc123",  # Wrong date format
        "20250120_14302-abc123",  # Wrong time format
        "abc123",  # No timestamp
        "20250120_143022",  # No process ID
        "processed",  # Legacy folder
        "screenshots"  # Other folder
    ]
    
    for pattern in valid_patterns:
        assert PathManager.is_session_folder(pattern), f"Should recognize valid pattern: {pattern}"
        print(f"✓ Valid pattern recognized: {pattern}")
    
    for pattern in invalid_patterns:
        assert not PathManager.is_session_folder(pattern), f"Should reject invalid pattern: {pattern}"
        print(f"✓ Invalid pattern rejected: {pattern}")

def test_file_operations():
    """Test file operations with the new folder structure"""
    print("\n=== Testing File Operations ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Initialize session
        process_id = str(uuid.uuid4())
        downloads_path, processed_path = initialize_session_paths(process_id)
        
        # Override the base directory for testing
        path_manager = get_path_manager()
        path_manager.base_downloads_dir = temp_dir
        session_folder = path_manager.create_session_folder(process_id)
        
        # Create test CSV file
        test_csv_content = "id,name,price\n1,Product A,10.99\n2,Product B,15.50\n"
        csv_file_path = os.path.join(session_folder, "test_products.csv")
        
        with open(csv_file_path, 'w') as f:
            f.write(test_csv_content)
        
        print(f"✓ Created test CSV file: {csv_file_path}")
        assert os.path.exists(csv_file_path), "Test CSV file should exist"
        
        # Test moving to processed directory
        processed_file_path = os.path.join(processed_path, "test_products.csv")
        shutil.move(csv_file_path, processed_file_path)
        
        print(f"✓ Moved file to processed: {processed_file_path}")
        assert os.path.exists(processed_file_path), "File should exist in processed directory"
        assert not os.path.exists(csv_file_path), "Original file should be moved"
        
        # Test timestamped filename creation
        timestamped_name = PathManager.create_timestamped_filename("test.csv")
        assert timestamped_name.endswith(".csv"), "Should preserve extension"
        assert "_" in timestamped_name, "Should contain timestamp separator"
        print(f"✓ Created timestamped filename: {timestamped_name}")

def test_multiple_sessions():
    """Test multiple concurrent sessions"""
    print("\n=== Testing Multiple Sessions ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        sessions = []
        
        # Create multiple sessions
        for i in range(3):
            process_id = f"test-process-{i}"
            path_manager = PathManager(base_downloads_dir=temp_dir)
            session_folder = path_manager.create_session_folder(process_id)
            sessions.append((process_id, session_folder, path_manager))
            
            # Create a test file in each session
            test_file = os.path.join(session_folder, f"test_{i}.csv")
            with open(test_file, 'w') as f:
                f.write(f"test,data,{i}\n")
            
            print(f"✓ Created session {i}: {os.path.basename(session_folder)}")
        
        # Verify all sessions are separate
        session_folders = [session[1] for session in sessions]
        assert len(set(session_folders)) == 3, "All session folders should be unique"
        
        # Verify all folders exist and contain their respective files
        for i, (process_id, session_folder, path_manager) in enumerate(sessions):
            test_file = os.path.join(session_folder, f"test_{i}.csv")
            assert os.path.exists(test_file), f"Test file should exist in session {i}"
            
            # Verify processed directory can be created independently
            processed_path = path_manager.get_processed_path()
            assert os.path.exists(processed_path), f"Processed directory should exist for session {i}"
        
        print(f"✓ All {len(sessions)} sessions are properly isolated")

def test_legacy_compatibility():
    """Test compatibility with legacy paths"""
    print("\n=== Testing Legacy Compatibility ===")
    
    from path_manager import get_legacy_downloads_path, get_legacy_processed_path
    
    # Test legacy path functions
    legacy_downloads = get_legacy_downloads_path()
    legacy_processed = get_legacy_processed_path()
    
    assert legacy_downloads == "/home/<USER>/10x-sales-agent/downloads", "Legacy downloads path should be correct"
    assert legacy_processed == "/home/<USER>/10x-sales-agent/downloads/processed", "Legacy processed path should be correct"
    
    print(f"✓ Legacy downloads path: {legacy_downloads}")
    print(f"✓ Legacy processed path: {legacy_processed}")

def test_utility_script_integration():
    """Test integration with updated utility scripts"""
    print("\n=== Testing Utility Script Integration ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create a session with test files
        process_id = "test-utility-integration"
        path_manager = PathManager(base_downloads_dir=temp_dir)
        session_folder = path_manager.create_session_folder(process_id)
        
        # Create test CSV files
        for i in range(2):
            csv_file = os.path.join(session_folder, f"test_{i}.csv")
            with open(csv_file, 'w') as f:
                f.write(f"id,name\n{i},Test Product {i}\n")
        
        print(f"✓ Created test session with CSV files: {os.path.basename(session_folder)}")
        
        # Test that session folder is recognized
        folder_name = os.path.basename(session_folder)
        assert PathManager.is_session_folder(folder_name), "Session folder should be recognized by utility scripts"
        
        # Verify files exist
        csv_files = [f for f in os.listdir(session_folder) if f.endswith('.csv')]
        assert len(csv_files) == 2, "Should have 2 CSV files"
        
        print(f"✓ Session contains {len(csv_files)} CSV files")
        print(f"✓ Utility scripts can identify session folder: {folder_name}")

def run_all_tests():
    """Run all tests"""
    print("Starting tests for new timestamp-processID folder structure...\n")
    
    try:
        test_path_manager_basic()
        test_session_folder_pattern()
        test_file_operations()
        test_multiple_sessions()
        test_legacy_compatibility()
        test_utility_script_integration()
        
        print("\n" + "="*60)
        print("🎉 ALL TESTS PASSED! 🎉")
        print("The new timestamp-processID folder structure is working correctly.")
        print("="*60)
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = run_all_tests()
    exit(0 if success else 1)
