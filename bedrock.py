import argparse
import asyncio
import os
import sys

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

import boto3  # type: ignore
from botocore.config import Config
from langchain_aws import ChatBedrockConverse  # type: ignore
from browser_use import Agent, Controller
from browser_use.browser import BrowserProfile
from browser_use.browser import BrowserSession
#from browser_use.mcp.client import MCPClient


def get_llm():
	config = Config(retries={'max_attempts': 10, 'mode': 'adaptive'})
	bedrock_client = boto3.client('bedrock-runtime', region_name='us-west-2', config=config)

	return ChatBedrockConverse(
		model_id="us.anthropic.claude-3-5-sonnet-20241022-v2:0",
		temperature=0.0,
		max_tokens=None,
		client=bedrock_client,
	)

extend_system_message = """
Always follow the below rule.
1. Think step by step before you take any action.
2. When performing a search task, prioritize opening https://www.bing.com for searching.
3. If an Ad is shown, you should click the "Skip/Close/Cancel" button to close it.
4. The final output should answer the user's question in English.
"""

# Define the default task for the agent
default_task = "Predict the weather conditions in Wuxi, Jiangsu, China within the next 2 weeks, Using China's temperature measurement units."

parser = argparse.ArgumentParser()
parser.add_argument('--query', type=str, help='The query for the agent to execute', default=default_task)
parser.add_argument('--query-file', type=str, help='File containing the query for the agent to execute')
args = parser.parse_args()

# Read query from file if specified, otherwise use command line argument
if args.query_file and os.path.exists(args.query_file):
    with open(args.query_file, 'r', encoding='utf-8') as f:
        task = f.read().strip()
    print(f"Read query from file: {args.query_file}")
else:
    task = args.query

llm = get_llm()

browser_session = BrowserSession(
	browser_profile=BrowserProfile(
		downloads_path='~/Downloads',
		user_data_dir='~/.config/browseruse/profiles/default',
	)
)

controller = Controller()
'''    
# Connect to multiple MCP servers
filesystem_client = MCPClient(
    server_name="filesystem",
    command="npx",
    args=["-y", "@modelcontextprotocol/server-filesystem", "/home/<USER>/Downloads"]
)

# Connect and register tools from both servers
filesystem_client.connect()
filesystem_client.register_to_controller(controller)
'''

initial_actions = [
	{'go_to_url': {'url': 'https://www.seerfar.com/admin/sign-in.html', 'new_tab': True}},
]

agent = Agent(
        task=task,
        llm=llm,                      # optional: pass a specific playwright page to start on
        browser_session=browser_session,  # optional: pass an existing browser session to an agent
		use_vision=True,
		message_context=extend_system_message,
		controller=controller,  # Controller has tools from both MCP servers
		validate_output=True,
		initial_actions=initial_actions,
    )

async def main():
	await agent.run(max_steps=30)

asyncio.run(main())
