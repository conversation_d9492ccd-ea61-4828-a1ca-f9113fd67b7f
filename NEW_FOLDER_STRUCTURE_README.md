# New Timestamp-ProcessID Folder Structure

## Overview

The file download and processing structure has been updated to organize files by process ID with timestamps. This provides better organization, prevents conflicts between concurrent processes, and makes it easier to track files from specific processing sessions.

## New Structure

### Before (Legacy)
```
/downloads/
├── file1.csv
├── file2.csv
└── processed/
    ├── old_file1.csv
    └── old_file2.csv
```

### After (New Structure)
```
/downloads/
├── 20250720_154500-abc123-def456-ghi789/
│   ├── file1.csv
│   ├── file2.csv
│   └── processed/
│       ├── processed_file1.csv
│       └── processed_file2.csv
├── 20250720_155000-xyz789-uvw456-rst123/
│   ├── another_file.csv
│   └── processed/
│       └── processed_another_file.csv
└── processed/  # Legacy processed folder (still supported)
    └── legacy_files.csv
```

## Folder Naming Convention

Session folders follow the pattern: `YYYYMMDD_HHMMSS-<process_id>`

- `YYYYMMDD_HHMMSS`: Timestamp when the session was created (UTC)
- `<process_id>`: Unique process identifier (UUID)

Examples:
- `20250720_154500-abc123-def456-ghi789-012345`
- `20250720_155000-process-name-with-dashes`

## Implementation Details

### Core Components

1. **PathManager Class** (`path_manager.py`)
   - Manages session folder creation and path resolution
   - Provides utilities for timestamp-processID folder operations
   - Handles fallback to legacy paths when needed

2. **Updated Functions**
   - `e2b_browser_use.main()` - Now accepts `process_id` parameter
   - `download_files_from_sandbox()` - Uses session-specific paths
   - `download_temp_files_from_multiple_dirs()` - Uses session-specific paths
   - `move_csv_files_to_processed()` - Uses session-specific processed directory

3. **Updated Scripts**
   - `move_csv_to_processed.py` - Can process specific session folders
   - `simple_csv_converter.py` - Can process specific session folders
   - `cleanup_duplicate_csvs.py` - Can process specific session folders

### API Integration

The FastAPI application (`app.py`) has been updated to:
1. Initialize session paths when a new process is created
2. Pass the process_id to `e2b_browser_use.main()`
3. Use session-specific paths for all file operations

## Usage

### For New Processes

When starting a new process, the system automatically:
1. Generates a unique process ID
2. Creates a timestamp-processID folder
3. Sets up downloads and processed subdirectories
4. Uses these paths for all file operations

```python
# This happens automatically in the FastAPI app
process_id = str(uuid.uuid4())
downloads_path, processed_path = initialize_session_paths(process_id)

# Call e2b automation with process_id
results = e2b_browser_use.main(query=prompt, process_id=process_id)
```

### For Utility Scripts

#### Process All Session Folders
```bash
# Process all session folders automatically
python3 move_csv_to_processed.py
python3 simple_csv_converter.py
python3 cleanup_duplicate_csvs.py
```

#### Process Specific Session Folder
```bash
# Process a specific session folder
python3 move_csv_to_processed.py /path/to/downloads/20250720_154500-abc123-def456/
python3 simple_csv_converter.py /path/to/downloads/20250720_154500-abc123-def456/
python3 cleanup_duplicate_csvs.py /path/to/downloads/20250720_154500-abc123-def456/
```

### Programmatic Usage

```python
from path_manager import PathManager, initialize_session_paths

# Create a new session
process_id = "my-process-id"
downloads_path, processed_path = initialize_session_paths(process_id)

# Use the paths for file operations
csv_file = os.path.join(downloads_path, "data.csv")
processed_file = os.path.join(processed_path, "data.csv")

# Move file to processed
shutil.move(csv_file, processed_file)
```

## Backward Compatibility

The system maintains full backward compatibility:

1. **Legacy Path Support**: All functions fall back to legacy paths when no session is active
2. **Existing Files**: Files in the legacy `/downloads/` and `/downloads/processed/` directories continue to work
3. **Utility Scripts**: Can process both legacy directories and new session folders

## Benefits

1. **Process Isolation**: Each process has its own folder, preventing file conflicts
2. **Better Organization**: Files are organized by timestamp and process ID
3. **Easier Debugging**: Can easily identify files from specific processing sessions
4. **Concurrent Processing**: Multiple processes can run simultaneously without interference
5. **Audit Trail**: Timestamp in folder name provides clear processing history

## Testing

Comprehensive tests have been created to verify the new structure:

- `test_new_folder_structure.py` - Tests core PathManager functionality
- `test_path_integration.py` - Tests integration and workflow scenarios

Run tests:
```bash
python3 test_new_folder_structure.py
python3 test_path_integration.py
```

## Migration

No migration is required. The system automatically:
1. Uses new structure for new processes
2. Continues to support legacy structure for existing files
3. Utility scripts can process both old and new structures

## File Locations

- **PathManager**: `path_manager.py`
- **Updated e2b module**: `e2b_browser_use.py`
- **Updated API**: `app.py`
- **Updated utilities**: `move_csv_to_processed.py`, `simple_csv_converter.py`, `cleanup_duplicate_csvs.py`
- **Tests**: `test_new_folder_structure.py`, `test_path_integration.py`
- **Documentation**: `NEW_FOLDER_STRUCTURE_README.md`
