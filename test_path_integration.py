#!/usr/bin/env python3
"""
Simple integration test for path management functionality.

This test verifies the core path management works correctly without
requiring external dependencies like dotenv, e2b, etc.
"""

import os
import tempfile
import uuid
import sys

# Add the current directory to Python path to import our modules
sys.path.insert(0, '/home/<USER>/10x-sales-agent')

from path_manager import (
    PathManager, 
    initialize_session_paths, 
    get_path_manager,
    get_current_downloads_path,
    get_current_processed_path,
    get_legacy_downloads_path,
    get_legacy_processed_path
)

def test_session_initialization():
    """Test session initialization with process ID"""
    print("=== Testing Session Initialization ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Override global path manager base directory
        original_base_dir = get_path_manager().base_downloads_dir
        get_path_manager().base_downloads_dir = temp_dir
        
        try:
            process_id = str(uuid.uuid4())
            
            # Initialize session paths
            downloads_path, processed_path = initialize_session_paths(process_id)
            
            # Verify paths are created
            assert os.path.exists(downloads_path), "Downloads path should exist"
            assert os.path.exists(processed_path), "Processed path should exist"
            
            # Verify path structure
            folder_name = os.path.basename(downloads_path)
            assert PathManager.is_session_folder(folder_name), "Should follow timestamp-processID pattern"
            assert processed_path == os.path.join(downloads_path, "processed"), "Processed should be subdirectory"
            
            # Test current path getters
            current_downloads = get_current_downloads_path()
            current_processed = get_current_processed_path()
            
            assert current_downloads == downloads_path, "Current downloads path should match"
            assert current_processed == processed_path, "Current processed path should match"
            
            print(f"✓ Session initialized for process: {process_id}")
            print(f"✓ Downloads path: {downloads_path}")
            print(f"✓ Processed path: {processed_path}")
            print(f"✓ Folder pattern: {folder_name}")
            
        finally:
            # Restore original base directory
            get_path_manager().base_downloads_dir = original_base_dir

def test_fallback_behavior():
    """Test fallback to legacy paths when no session is active"""
    print("\n=== Testing Fallback Behavior ===")
    
    # Reset global path manager state
    get_path_manager()._session_folder = None
    get_path_manager()._process_id = None
    
    try:
        # This should raise RuntimeError since no session is active
        get_current_downloads_path()
        assert False, "Should raise RuntimeError when no session is active"
    except RuntimeError:
        print("✓ Correctly raises RuntimeError when no session is active")
    
    # Test legacy path functions
    legacy_downloads = get_legacy_downloads_path()
    legacy_processed = get_legacy_processed_path()
    
    expected_downloads = "/home/<USER>/10x-sales-agent/downloads"
    expected_processed = "/home/<USER>/10x-sales-agent/downloads/processed"
    
    assert legacy_downloads == expected_downloads, "Legacy downloads path should be correct"
    assert legacy_processed == expected_processed, "Legacy processed path should be correct"
    
    print(f"✓ Legacy downloads path: {legacy_downloads}")
    print(f"✓ Legacy processed path: {legacy_processed}")

def test_multiple_process_isolation():
    """Test that multiple processes create isolated folders"""
    print("\n=== Testing Multiple Process Isolation ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        process_sessions = []
        
        # Create multiple process sessions
        for i in range(3):
            process_id = f"test-process-{i}"
            path_manager = PathManager(base_downloads_dir=temp_dir)
            session_folder = path_manager.create_session_folder(process_id)
            
            # Create a test file in each session
            test_file = os.path.join(session_folder, f"test_{i}.csv")
            with open(test_file, 'w') as f:
                f.write(f"process,{i}\ndata,test\n")
            
            process_sessions.append((process_id, session_folder, test_file))
            print(f"✓ Created session {i}: {os.path.basename(session_folder)}")
        
        # Verify all sessions are isolated
        session_folders = [session[1] for session in process_sessions]
        assert len(set(session_folders)) == 3, "All session folders should be unique"
        
        # Verify each session has its own files
        for i, (process_id, session_folder, test_file) in enumerate(process_sessions):
            assert os.path.exists(test_file), f"Test file should exist in session {i}"
            
            # Read and verify file content
            with open(test_file, 'r') as f:
                content = f.read()
                assert f"process,{i}" in content, f"File should contain correct process data for session {i}"
        
        print(f"✓ All {len(process_sessions)} sessions are properly isolated")

def test_file_organization_simulation():
    """Simulate the file organization workflow"""
    print("\n=== Testing File Organization Workflow ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Override global path manager
        original_base_dir = get_path_manager().base_downloads_dir
        get_path_manager().base_downloads_dir = temp_dir
        
        try:
            process_id = "workflow-test-process"
            
            # Step 1: Initialize session
            downloads_path, processed_path = initialize_session_paths(process_id)
            print(f"✓ Step 1: Session initialized")
            
            # Step 2: Simulate file download
            csv_files = []
            for i in range(2):
                csv_file = os.path.join(downloads_path, f"download_{i}.csv")
                with open(csv_file, 'w') as f:
                    f.write(f"id,name,price\n{i},Product {i},{10.99 + i}\n")
                csv_files.append(csv_file)
            
            print(f"✓ Step 2: Downloaded {len(csv_files)} CSV files")
            
            # Step 3: Simulate file processing and moving to processed
            for csv_file in csv_files:
                filename = os.path.basename(csv_file)
                processed_file = os.path.join(processed_path, filename)
                
                # Simulate processing (just copy for this test)
                import shutil
                shutil.move(csv_file, processed_file)
                
                assert os.path.exists(processed_file), "File should exist in processed directory"
                assert not os.path.exists(csv_file), "Original file should be moved"
            
            print(f"✓ Step 3: Moved {len(csv_files)} files to processed directory")
            
            # Step 4: Verify final state
            downloads_files = [f for f in os.listdir(downloads_path) if f.endswith('.csv')]
            processed_files = [f for f in os.listdir(processed_path) if f.endswith('.csv')]
            
            assert len(downloads_files) == 0, "Downloads directory should be empty"
            assert len(processed_files) == 2, "Processed directory should have 2 files"
            
            print(f"✓ Step 4: Final state verified - {len(processed_files)} files in processed")
            
            # Step 5: Verify folder structure
            folder_name = os.path.basename(downloads_path)
            assert PathManager.is_session_folder(folder_name), "Folder should follow naming convention"
            
            print(f"✓ Step 5: Folder structure verified: {folder_name}")
            
        finally:
            # Restore original base directory
            get_path_manager().base_downloads_dir = original_base_dir

def run_path_integration_tests():
    """Run all path integration tests"""
    print("Starting path management integration tests...\n")
    
    try:
        test_session_initialization()
        test_fallback_behavior()
        test_multiple_process_isolation()
        test_file_organization_simulation()
        
        print("\n" + "="*60)
        print("🎉 ALL PATH INTEGRATION TESTS PASSED! 🎉")
        print("Path management system is working correctly.")
        print("="*60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ PATH INTEGRATION TEST FAILED: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = run_path_integration_tests()
    exit(0 if success else 1)
