#!/usr/bin/env python3
"""
Test script to verify the CSV detection fix works correctly
"""

import os
import glob
import shutil
import tempfile

def test_csv_detection_logic():
    """Test the CSV detection logic from the fixed app.py"""
    
    # Test with the actual downloads directory
    downloads_path = "/home/<USER>/10x-sales-agent/downloads"
    
    print(f"Testing CSV detection logic with: {downloads_path}")
    print("=" * 60)
    
    # Test 1: Check current state
    print("TEST 1: Current state analysis")
    print("-" * 30)
    
    # Look for any CSV files (not just Seerfar-Product pattern)
    csv_files = []
    all_files_found = glob.glob(os.path.join(downloads_path, "*.csv"))
    print(f"Found {len(all_files_found)} total CSV files in downloads_path")
    
    for file in all_files_found:
        print(f"Checking file: {file}")
        # Skip files in processed subdirectory
        if 'processed' not in file:
            csv_files.append(file)
            print(f"  ✅ Added to csv_files: {file}")
        else:
            print(f"  ⏭️  Skipped processed file: {file}")

    print(f"Result: Found {len(csv_files)} CSV files in main downloads directory")
    
    # Test 2: Check processed directory
    print(f"\nTEST 2: Processed directory analysis")
    print("-" * 30)
    
    processed_dir = os.path.join(downloads_path, "processed")
    if os.path.exists(processed_dir):
        processed_files = glob.glob(os.path.join(processed_dir, "*.csv"))
        print(f"Found {len(processed_files)} CSV files in processed directory:")
        for file in processed_files:
            print(f"  📁 {os.path.basename(file)}")
            
        if processed_files:
            # Get the most recent processed file
            latest_processed = max(processed_files, key=os.path.getctime)
            print(f"\nMost recent processed file: {os.path.basename(latest_processed)}")
            
            # Test the copy logic
            print(f"\nTEST 3: Copy logic simulation")
            print("-" * 30)
            
            filename = os.path.basename(latest_processed)
            copied_file = os.path.join(downloads_path, f"processing_{filename}")
            
            print(f"Would copy: {latest_processed}")
            print(f"To: {copied_file}")
            
            # Actually perform the copy for testing
            try:
                shutil.copy2(latest_processed, copied_file)
                print(f"✅ Successfully copied file for testing")
                
                # Verify the copied file exists and is readable
                if os.path.exists(copied_file):
                    file_size = os.path.getsize(copied_file)
                    print(f"✅ Copied file exists, size: {file_size:,} bytes")
                    
                    # Test reading the file
                    with open(copied_file, 'r', encoding='utf-8') as f:
                        first_line = f.readline().strip()
                        line_count = sum(1 for _ in f) + 1
                    
                    print(f"✅ File is readable, {line_count:,} lines")
                    print(f"✅ Header: {first_line[:100]}...")
                    
                    # Clean up the test file
                    os.remove(copied_file)
                    print(f"✅ Cleaned up test file")
                    
                else:
                    print(f"❌ Copied file does not exist")
                    
            except Exception as e:
                print(f"❌ Error during copy test: {e}")
    else:
        print(f"❌ Processed directory does not exist: {processed_dir}")
    
    # Test 4: Final recommendation
    print(f"\nTEST 4: Logic flow simulation")
    print("-" * 30)
    
    if not csv_files:
        print("✅ No CSV files in main directory - would check processed directory")
        if os.path.exists(processed_dir):
            processed_files = glob.glob(os.path.join(processed_dir, "*.csv"))
            if processed_files:
                latest_processed = max(processed_files, key=os.path.getctime)
                print(f"✅ Would copy back: {os.path.basename(latest_processed)}")
                print(f"✅ API would process the copied file")
                print(f"✅ After processing, would delete the temporary copy")
            else:
                print(f"❌ Would raise: No CSV files found in processed directory")
        else:
            print(f"❌ Would raise: Processed directory does not exist")
    else:
        latest_file = max(csv_files, key=os.path.getctime)
        print(f"✅ Would process file from main directory: {os.path.basename(latest_file)}")
        print(f"✅ After processing, would move to processed directory")
    
    print(f"\n🎉 CSV detection logic test completed!")

if __name__ == "__main__":
    test_csv_detection_logic()
